# 充电场站评分系统 - 数据存储和计算策略详细说明

## 一、Redis vs MySQL 职责分工

### 1.1 Redis的作用（临时缓存）
```python
# Redis只做性能优化，不做持久存储
REDIS_RESPONSIBILITIES = {
    "缓存热点数据": "提升API查询速度",
    "临时状态存储": "计算任务进度、锁机制",
    "会话管理": "用户会话、API限流计数",
    "实时计算结果": "避免重复计算"
}

# 典型缓存场景
CACHE_SCENARIOS = {
    "高频查询": "排行榜、统计数据",
    "复杂计算": "聚合查询结果",
    "实时状态": "计算进度、任务状态",
    "临时数据": "中间计算结果"
}
```

### 1.2 MySQL的作用（持久存储）
```python
# MySQL是唯一的数据持久化存储
MYSQL_RESPONSIBILITIES = {
    "历史评分": "所有月度评分数据永久保存",
    "详细指标": "各项计算指标完整记录",
    "趋势分析": "支持历史对比和趋势分析",
    "审计日志": "计算过程和结果可追溯"
}

# 数据表职责
TABLE_PURPOSES = {
    "station_scores": "月度评分主表，支持历史查询",
    "station_score_details": "详细指标，支持深度分析",
    "station_score_trends": "变化趋势，支持对比分析",
    "score_calculation_logs": "计算日志，支持问题排查"
}
```

## 二、历史数据保存策略

### 2.1 完整历史保存
```sql
-- 示例：某场站的历史评分记录
SELECT 
    station_name,
    stat_period,
    total_score,
    grade,
    city_rank,
    create_time
FROM station_scores 
WHERE station_id = '123'
ORDER BY stat_period DESC;

-- 结果示例
+------------------+-------------+-------------+-------+----------+---------------------+
| station_name     | stat_period | total_score | grade | city_rank| create_time         |
+------------------+-------------+-------------+-------+----------+---------------------+
| 北京朝阳充电站   | 2025-01     | 85.6        | A     | 5        | 2025-02-01 02:00:00 |
| 北京朝阳充电站   | 2024-12     | 83.2        | A     | 7        | 2025-01-01 02:00:00 |
| 北京朝阳充电站   | 2024-11     | 81.8        | B     | 12       | 2024-12-01 02:00:00 |
| 北京朝阳充电站   | 2024-10     | 79.5        | B     | 15       | 2024-11-01 02:00:00 |
+------------------+-------------+-------------+-------+----------+---------------------+
```

### 2.2 数据分层存储
```python
# 根据数据访问频率分层存储
DATA_TIERS = {
    "热数据": {
        "范围": "最近3个月",
        "存储": "主表，SSD存储",
        "索引": "全量索引",
        "查询": "毫秒级响应"
    },
    "温数据": {
        "范围": "3-12个月",
        "存储": "主表，可能分区",
        "索引": "主要索引",
        "查询": "秒级响应"
    },
    "冷数据": {
        "范围": "12个月以上",
        "存储": "归档表或分区",
        "索引": "基础索引",
        "查询": "分钟级响应"
    }
}
```

### 2.3 数据归档策略
```sql
-- 创建归档表（结构相同）
CREATE TABLE station_scores_archive LIKE station_scores;

-- 定期归档策略（保留2年热数据）
INSERT INTO station_scores_archive 
SELECT * FROM station_scores 
WHERE STR_TO_DATE(stat_period, '%Y-%m') < DATE_SUB(CURDATE(), INTERVAL 24 MONTH);

-- 删除已归档的数据（可选，根据存储成本决定）
DELETE FROM station_scores 
WHERE STR_TO_DATE(stat_period, '%Y-%m') < DATE_SUB(CURDATE(), INTERVAL 24 MONTH);
```

## 三、首次计算数据范围策略

### 3.1 数据范围确定原则
```python
def determine_initial_calculation_range():
    """确定首次计算的数据范围"""
    
    # 1. 检查数据可用性
    earliest_data = get_earliest_order_date()
    latest_data = get_latest_order_date()
    
    # 2. 确定计算范围
    if (latest_data - earliest_data).days >= 180:  # 有6个月以上数据
        # 使用最近6个月数据
        end_date = latest_data.replace(day=1) - timedelta(days=1)  # 上月末
        start_date = end_date - timedelta(days=180)  # 往前6个月
        calculation_type = "STANDARD"
    elif (latest_data - earliest_data).days >= 90:  # 有3个月以上数据
        # 使用最近3个月数据
        end_date = latest_data.replace(day=1) - timedelta(days=1)
        start_date = end_date - timedelta(days=90)
        calculation_type = "MINIMUM"
    else:
        # 数据不足，不进行评分
        return None, "数据不足3个月，无法进行评分"
    
    return {
        "start_date": start_date,
        "end_date": end_date,
        "calculation_type": calculation_type,
        "data_months": get_months_between(start_date, end_date)
    }
```

### 3.2 场站筛选条件
```python
# 首次计算的场站筛选条件
STATION_FILTER_CRITERIA = {
    "基础条件": {
        "status": 4,                    # 已投运状态
        "del_flag": 0,                  # 未删除
        "dc_ac_num": "≥ 1"             # 至少1台设备
    },
    "运营条件": {
        "运营天数": "≥ 30天",           # 至少运营1个月
        "订单数量": "≥ 50笔",           # 至少50笔订单
        "收益金额": "≥ 1000元"          # 至少1000元收益
    },
    "数据质量": {
        "完整性": "订单数据完整",
        "有效性": "无异常数据",
        "连续性": "运营相对连续"
    }
}

def filter_eligible_stations(start_date, end_date):
    """筛选符合条件的场站"""
    sql = """
    SELECT 
        ps.id as station_id,
        ps.name as station_name,
        ps.city_name,
        ps.dc_ac_num,
        COUNT(pos.id) as order_count,
        SUM(pos.total_fees) as total_revenue,
        DATEDIFF(MAX(pos.start_time), MIN(pos.start_time)) + 1 as operating_days
    FROM pile_station ps
    INNER JOIN pile_order_snapshot pos ON ps.id = pos.station_id
    WHERE ps.status = 4 
        AND ps.del_flag = 0
        AND ps.dc_ac_num >= 1
        AND pos.status = 4
        AND pos.start_time BETWEEN %s AND %s
    GROUP BY ps.id
    HAVING order_count >= 50 
        AND total_revenue >= 1000
        AND operating_days >= 30
    """
    return execute_query(sql, [start_date, end_date])
```

### 3.3 分批计算策略
```python
def execute_initial_calculation():
    """执行首次评分计算"""
    
    # 1. 确定计算范围
    calc_range = determine_initial_calculation_range()
    if not calc_range:
        return {"status": "failed", "message": "数据不足"}
    
    # 2. 筛选符合条件的场站
    eligible_stations = filter_eligible_stations(
        calc_range["start_date"], 
        calc_range["end_date"]
    )
    
    # 3. 按月份分别计算
    results = []
    for month in calc_range["data_months"]:
        month_results = calculate_monthly_scores(
            eligible_stations, 
            month["start"], 
            month["end"]
        )
        results.extend(month_results)
    
    # 4. 保存结果
    save_calculation_results(results)
    
    return {
        "status": "success",
        "calculation_range": calc_range,
        "stations_count": len(eligible_stations),
        "months_calculated": len(calc_range["data_months"]),
        "total_scores": len(results)
    }
```

## 四、月度更新机制

### 4.1 定时计算任务
```python
from apscheduler.schedulers.blocking import BlockingScheduler

def setup_monthly_calculation():
    """设置月度计算任务"""
    scheduler = BlockingScheduler()
    
    # 每月1号凌晨2点执行
    scheduler.add_job(
        func=calculate_monthly_scores_job,
        trigger="cron",
        day=1,
        hour=2,
        minute=0,
        id='monthly_score_calculation'
    )
    
    scheduler.start()

def calculate_monthly_scores_job():
    """月度评分计算任务"""
    # 计算上个月的评分
    last_month = get_last_month_period()
    
    # 执行计算
    result = calculate_scores_for_period(last_month)
    
    # 更新缓存
    update_redis_cache(last_month)
    
    # 发送通知
    notify_calculation_complete(result)
```

### 4.2 增量更新策略
```python
def incremental_update_strategy():
    """增量更新策略"""
    
    # 只计算有新数据的场站
    stations_with_new_data = get_stations_with_recent_orders()
    
    # 只更新变化的评分
    for station in stations_with_new_data:
        new_score = calculate_station_score(station)
        old_score = get_cached_score(station)
        
        if score_changed_significantly(new_score, old_score):
            update_station_score(station, new_score)
            invalidate_related_cache(station)
```

## 五、缓存更新策略

### 5.1 缓存失效机制
```python
def cache_invalidation_strategy():
    """缓存失效策略"""
    
    # 1. 计算完成后立即失效相关缓存
    def invalidate_after_calculation(station_id, period):
        keys_to_delete = [
            f"station:score:{station_id}:{period}",
            f"city:ranking:{get_city(station_id)}:{period}",
            f"national:ranking:{period}",
            f"score:stats:{period}"
        ]
        redis_client.delete(*keys_to_delete)
    
    # 2. 定时清理过期缓存
    def cleanup_expired_cache():
        # Redis自动过期，无需手动清理
        pass
    
    # 3. 手动刷新缓存
    def manual_cache_refresh(cache_type, **kwargs):
        if cache_type == "station_score":
            refresh_station_score_cache(kwargs["station_id"])
        elif cache_type == "rankings":
            refresh_ranking_cache(kwargs.get("city_name"))
```

### 5.2 缓存预热策略
```python
def cache_warmup_strategy():
    """缓存预热策略"""
    
    # 计算完成后预热热点数据
    def warmup_after_calculation(period):
        # 1. 预热全国排行榜前50
        top_stations = get_top_stations_national(period, 50)
        cache_ranking_data("national", period, top_stations)
        
        # 2. 预热主要城市排行榜
        major_cities = get_major_cities()
        for city in major_cities:
            city_ranking = get_city_ranking(city, period)
            cache_ranking_data(city, period, city_ranking)
        
        # 3. 预热统计数据
        stats = calculate_score_statistics(period)
        cache_statistics_data(period, stats)
```

## 六、数据一致性保证

### 6.1 事务处理
```python
def ensure_data_consistency():
    """确保数据一致性"""
    
    with database.transaction():
        try:
            # 1. 计算评分
            score_data = calculate_station_score(station_id)
            
            # 2. 保存主评分
            save_station_score(score_data)
            
            # 3. 保存详细指标
            save_score_details(score_data)
            
            # 4. 更新趋势数据
            update_score_trends(score_data)
            
            # 5. 记录计算日志
            log_calculation_success(station_id)
            
            # 6. 提交事务后更新缓存
            database.commit()
            update_cache_after_commit(station_id)
            
        except Exception as e:
            database.rollback()
            log_calculation_error(station_id, str(e))
            raise
```

### 6.2 数据校验
```python
def validate_calculation_results():
    """校验计算结果"""
    
    # 1. 数据完整性校验
    missing_details = check_missing_score_details()
    if missing_details:
        logger.warning(f"发现{len(missing_details)}个场站缺少详细指标")
    
    # 2. 数据逻辑校验
    invalid_scores = check_invalid_scores()
    if invalid_scores:
        logger.error(f"发现{len(invalid_scores)}个无效评分")
    
    # 3. 排名一致性校验
    ranking_issues = check_ranking_consistency()
    if ranking_issues:
        logger.error(f"发现{len(ranking_issues)}个排名不一致问题")
```

这样的设计确保了：
1. **Redis只做缓存**，不承担数据持久化责任
2. **MySQL保存所有历史数据**，支持完整的历史分析
3. **首次计算使用6个月数据**，确保评分的准确性和稳定性
4. **月度更新机制**，保持数据的时效性
5. **完整的数据一致性保证**，确保系统的可靠性
