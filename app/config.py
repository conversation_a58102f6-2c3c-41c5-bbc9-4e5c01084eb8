"""
配置文件
"""
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = "充电场站评分系统"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    database_url: str = "mysql+pymysql://user:password@localhost:3306/chargepile-v3.0"
    database_echo: bool = True #True for test
    
    # API配置
    api_v1_prefix: str = "/api/v1"
    
    # 评分计算配置
    score_calculation_batch_size: int = 50
    score_calculation_timeout: int = 3600  # 1小时
    
    # 数据质量要求
    min_operating_days: int = 30
    min_orders_count: int = 50
    min_revenue_amount: float = 1000.0
    
    # 权重配置
    operation_efficiency_weight: float = 0.5
    service_quality_weight: float = 0.2
    stability_weight: float = 0.3
    
    
    # 运营效率指标权重
    daily_revenue_rate_weight: float = 0.30
    device_utilization_weight: float = 0.25
    revenue_per_device_weight: float = 0.25
    completion_rate_weight: float = 0.20
    
    # 服务质量指标权重
    avg_charge_time_weight: float = 0.25
    avg_charge_power_weight: float = 0.30
    user_retention_rate_weight: float = 0.30
    failure_rate_weight: float = 0.15
    
    # 稳定性指标权重
    order_growth_trend_weight: float = 0.20
    revenue_stability_weight: float = 0.25
    operation_continuity_weight: float = 0.20
    device_health_rate_weight: float = 0.20
    time_balance_index_weight: float = 0.15
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = "logs/app.log"
    
    # 定时任务配置
    enable_scheduler: bool = True
    monthly_calculation_hour: int = 2  # 凌晨2点执行
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()


# 评分权重配置
SCORE_WEIGHTS = {
    "dimensions": {
        "operation_efficiency": settings.operation_efficiency_weight,
        "service_quality": settings.service_quality_weight,
        "stability": settings.stability_weight
    },
    "operation_metrics": {
        "daily_revenue_rate": settings.daily_revenue_rate_weight,
        "device_utilization": settings.device_utilization_weight,
        "revenue_per_device": settings.revenue_per_device_weight,
        "completion_rate": settings.completion_rate_weight
    },
    "service_metrics": {
        "avg_charge_time": settings.avg_charge_time_weight,
        "avg_charge_power": settings.avg_charge_power_weight,
        "user_retention_rate": settings.user_retention_rate_weight,
        "failure_rate": settings.failure_rate_weight
    },
    "stability_metrics": {
        "order_growth_trend": settings.order_growth_trend_weight,
        "revenue_stability": settings.revenue_stability_weight,
        "operation_continuity": settings.operation_continuity_weight,
        "device_health_rate": settings.device_health_rate_weight,
        "time_balance_index": settings.time_balance_index_weight
    }
}


# 数据质量要求
DATA_QUALITY_REQUIREMENTS = {
    "station": {
        "min_operating_days": settings.min_operating_days,
        "min_orders_count": settings.min_orders_count,
        "min_revenue_amount": settings.min_revenue_amount
    },
    "order": {
        "valid_statuses": [4],  # 只考虑充电完成订单
        "min_charge_time": 0.1,  # 最小充电时长6分钟
        "max_charge_time": 24.0,  # 最大充电时长24小时
        "min_charge_power": 0.1,  # 最小充电量
        "min_total_fees": 0.1   # 最小费用
    }
}


# 等级划分
GRADE_THRESHOLDS = {
    "S": 90.0,
    "A": 80.0,
    "B": 70.0,
    "C": 60.0,
    "D": 0.0
}


def get_grade(score: float) -> str:
    """根据分数获取等级"""
    for grade, threshold in GRADE_THRESHOLDS.items():
        if score >= threshold:
            return grade
    return "D"
