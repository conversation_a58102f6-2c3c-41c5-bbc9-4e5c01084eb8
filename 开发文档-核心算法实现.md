# 充电场站评分系统 - 核心算法实现文档

## 一、算法概述

### 1.1 评分体系
采用三维评分模型，权重分配如下：
- **运营效率 (50%)**：衡量场站的经营效果和资源利用率
- **服务质量 (30%)**：衡量用户体验和服务水平
- **稳定性 (20%)**：衡量运营的持续性和可靠性

### 1.2 计算流程
```
原始数据 → 数据清洗 → 指标计算 → 标准化处理 → 权重计算 → 综合评分 → 等级划分
```

## 二、数据预处理

### 2.1 数据源定义
```python
# 主要数据表
SOURCE_TABLES = {
    'stations': 'pile_station',           # 场站基础信息
    'devices': 'pile_device',             # 设备信息  
    'orders': 'pile_order_snapshot',      # 订单快照
    'users': 'app_user'                   # 用户信息
}

# 数据筛选条件
FILTER_CONDITIONS = {
    'station_status': 4,                  # 已投运场站
    'device_type': 1,                     # 充电车
    'order_status': 4,                    # 充电完成订单
    'del_flag': 0                         # 未删除数据
}
```

### 2.2 数据清洗规则
```python
def clean_data(df):
    """数据清洗函数"""
    # 1. 移除异常值
    df = df[df['total_fees'] > 0]                    # 费用大于0
    df = df[df['charge_power'] > 0]                  # 充电量大于0
    df = df[df['timelong'] > 0.1]                    # 充电时长大于6分钟
    df = df[df['timelong'] < 24]                     # 充电时长小于24小时
    
    # 2. 处理缺失值
    df['server_fees'].fillna(0, inplace=True)       # 服务费默认0
    df['end_soc'].fillna(df['start_soc'] + 20, inplace=True)  # SOC估算
    
    # 3. 数据类型转换
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['end_time'] = pd.to_datetime(df['end_time'])
    
    return df
```

## 三、核心指标计算

### 3.1 运营效率指标

#### 3.1.1 日均收益
```python
def calculate_daily_revenue(station_data):
    """计算日均收益"""
    total_revenue = station_data['total_fees'].sum()
    operating_days = (station_data['start_time'].max() - 
                     station_data['start_time'].min()).days + 1
    return total_revenue / max(operating_days, 1)
```

#### 3.1.2 设备利用率
```python
def calculate_device_utilization(station_data, device_count):
    """计算设备利用率"""
    total_orders = len(station_data)
    operating_days = (station_data['start_time'].max() - 
                     station_data['start_time'].min()).days + 1
    
    # 理论最大订单数 = 设备数 × 运营天数 × 每天最大订单数(假设12次)
    max_possible_orders = device_count * operating_days * 12
    return (total_orders / max_possible_orders) * 100
```

#### 3.1.3 充电完成率
```python
def calculate_completion_rate(all_orders, completed_orders):
    """计算充电完成率"""
    if len(all_orders) == 0:
        return 0
    return (len(completed_orders) / len(all_orders)) * 100
```

### 3.2 服务质量指标

#### 3.2.1 平均充电时长
```python
def calculate_avg_charge_time(station_data):
    """计算平均充电时长(小时)"""
    return station_data['timelong'].mean()
```

#### 3.2.2 平均充电量
```python
def calculate_avg_charge_power(station_data):
    """计算平均充电量(kWh)"""
    return station_data['charge_power'].mean()
```

#### 3.2.3 用户复购率
```python
def calculate_user_retention_rate(station_data):
    """计算用户复购率"""
    user_order_counts = station_data['person_user_id'].value_counts()
    repeat_users = (user_order_counts > 1).sum()
    total_users = len(user_order_counts)
    
    if total_users == 0:
        return 0
    return (repeat_users / total_users) * 100
```

### 3.3 稳定性指标

#### 3.3.1 收益稳定性
```python
def calculate_revenue_stability(station_data):
    """计算收益稳定性"""
    # 按日统计收益
    daily_revenue = station_data.groupby(
        station_data['start_time'].dt.date
    )['total_fees'].sum()
    
    if len(daily_revenue) < 2:
        return 0
    
    mean_revenue = daily_revenue.mean()
    std_revenue = daily_revenue.std()
    
    if mean_revenue == 0:
        return 0
    
    # 稳定性 = 1 - 变异系数
    cv = std_revenue / mean_revenue
    return max(0, (1 - cv) * 100)
```

#### 3.3.2 运营连续性
```python
def calculate_operation_continuity(station_data, total_days):
    """计算运营连续性"""
    # 统计有订单的天数
    operating_days = station_data['start_time'].dt.date.nunique()
    return (operating_days / total_days) * 100
```

## 四、评分标准化

### 4.1 Z-Score标准化
```python
def z_score_normalize(values):
    """Z-Score标准化"""
    mean_val = np.mean(values)
    std_val = np.std(values)
    
    if std_val == 0:
        return np.zeros_like(values)
    
    return (values - mean_val) / std_val
```

### 4.2 Min-Max标准化
```python
def min_max_normalize(values, target_min=0, target_max=100):
    """Min-Max标准化到指定范围"""
    min_val = np.min(values)
    max_val = np.max(values)
    
    if max_val == min_val:
        return np.full_like(values, target_min)
    
    normalized = (values - min_val) / (max_val - min_val)
    return normalized * (target_max - target_min) + target_min
```

### 4.3 分位数标准化
```python
def percentile_normalize(values):
    """基于分位数的标准化"""
    percentiles = np.percentile(values, [25, 50, 75, 90, 95])
    
    def score_mapping(value):
        if value >= percentiles[4]:    # 95分位数以上
            return 95 + (value - percentiles[4]) / (np.max(values) - percentiles[4]) * 5
        elif value >= percentiles[3]:  # 90-95分位数
            return 85 + (value - percentiles[3]) / (percentiles[4] - percentiles[3]) * 10
        elif value >= percentiles[2]:  # 75-90分位数
            return 70 + (value - percentiles[2]) / (percentiles[3] - percentiles[2]) * 15
        elif value >= percentiles[1]:  # 50-75分位数
            return 50 + (value - percentiles[1]) / (percentiles[2] - percentiles[1]) * 20
        elif value >= percentiles[0]:  # 25-50分位数
            return 25 + (value - percentiles[0]) / (percentiles[1] - percentiles[0]) * 25
        else:                          # 25分位数以下
            return (value - np.min(values)) / (percentiles[0] - np.min(values)) * 25
    
    return np.array([score_mapping(v) for v in values])
```

## 五、综合评分计算

### 5.1 权重配置
```python
SCORE_WEIGHTS = {
    'operation_efficiency': 0.5,    # 运营效率 50%
    'service_quality': 0.3,         # 服务质量 30%
    'stability': 0.2                # 稳定性 20%
}

METRIC_WEIGHTS = {
    'operation': {
        'daily_revenue': 0.4,        # 日均收益 40%
        'device_utilization': 0.35,  # 设备利用率 35%
        'completion_rate': 0.25      # 完成率 25%
    },
    'service': {
        'avg_charge_time': 0.3,      # 平均充电时长 30%
        'avg_charge_power': 0.4,     # 平均充电量 40%
        'user_retention_rate': 0.3   # 用户复购率 30%
    },
    'stability': {
        'revenue_stability': 0.6,    # 收益稳定性 60%
        'operation_continuity': 0.4  # 运营连续性 40%
    }
}
```

### 5.2 综合评分算法
```python
def calculate_comprehensive_score(metrics):
    """计算综合评分"""
    
    # 1. 计算各维度得分
    operation_score = (
        metrics['daily_revenue'] * METRIC_WEIGHTS['operation']['daily_revenue'] +
        metrics['device_utilization'] * METRIC_WEIGHTS['operation']['device_utilization'] +
        metrics['completion_rate'] * METRIC_WEIGHTS['operation']['completion_rate']
    )
    
    service_score = (
        metrics['avg_charge_time'] * METRIC_WEIGHTS['service']['avg_charge_time'] +
        metrics['avg_charge_power'] * METRIC_WEIGHTS['service']['avg_charge_power'] +
        metrics['user_retention_rate'] * METRIC_WEIGHTS['service']['user_retention_rate']
    )
    
    stability_score = (
        metrics['revenue_stability'] * METRIC_WEIGHTS['stability']['revenue_stability'] +
        metrics['operation_continuity'] * METRIC_WEIGHTS['stability']['operation_continuity']
    )
    
    # 2. 计算总分
    total_score = (
        operation_score * SCORE_WEIGHTS['operation_efficiency'] +
        service_score * SCORE_WEIGHTS['service_quality'] +
        stability_score * SCORE_WEIGHTS['stability']
    )
    
    return {
        'total_score': round(total_score, 2),
        'operation_score': round(operation_score, 2),
        'service_score': round(service_score, 2),
        'stability_score': round(stability_score, 2)
    }
```

## 六、等级划分

### 6.1 评分等级定义
```python
def get_grade(score):
    """根据分数获取等级"""
    if score >= 90:
        return 'S'
    elif score >= 80:
        return 'A'
    elif score >= 70:
        return 'B'
    elif score >= 60:
        return 'C'
    else:
        return 'D'

GRADE_DESCRIPTIONS = {
    'S': '优秀场站 - 各项指标表现卓越',
    'A': '良好场站 - 运营状况良好',
    'B': '一般场站 - 运营状况正常',
    'C': '待改进场站 - 存在改进空间',
    'D': '问题场站 - 需要重点关注'
}
```

### 6.2 排名计算
```python
def calculate_rankings(scores_df):
    """计算排名"""
    # 全国排名
    scores_df['national_rank'] = scores_df['total_score'].rank(
        method='dense', ascending=False
    ).astype(int)
    
    # 城市排名
    scores_df['city_rank'] = scores_df.groupby('city_name')['total_score'].rank(
        method='dense', ascending=False
    ).astype(int)
    
    return scores_df
```

## 七、异常处理

### 7.1 数据异常处理
```python
def handle_data_exceptions(station_data):
    """处理数据异常"""
    exceptions = []
    
    # 检查数据完整性
    if len(station_data) == 0:
        exceptions.append("无订单数据")
        return None, exceptions
    
    # 检查时间范围
    time_span = (station_data['start_time'].max() - 
                station_data['start_time'].min()).days
    if time_span < 7:
        exceptions.append("数据时间跨度不足7天")
    
    # 检查订单数量
    if len(station_data) < 10:
        exceptions.append("订单数量过少，可能影响评分准确性")
    
    return station_data, exceptions
```

### 7.2 计算异常处理
```python
def safe_calculate(func, *args, default=0):
    """安全计算，处理异常情况"""
    try:
        result = func(*args)
        if np.isnan(result) or np.isinf(result):
            return default
        return result
    except (ZeroDivisionError, ValueError, TypeError):
        return default
```

## 八、性能优化

### 8.1 批量计算优化
```python
def batch_calculate_scores(station_ids, batch_size=50):
    """批量计算评分"""
    results = []
    
    for i in range(0, len(station_ids), batch_size):
        batch = station_ids[i:i + batch_size]
        batch_results = calculate_batch_scores(batch)
        results.extend(batch_results)
        
        # 避免内存溢出
        if i % (batch_size * 10) == 0:
            gc.collect()
    
    return results
```

### 8.2 缓存策略
```python
def get_cached_calculation(station_id, period):
    """获取缓存的计算结果"""
    cache_key = f"score:{station_id}:{period}"
    cached_result = redis_client.get(cache_key)
    
    if cached_result:
        return json.loads(cached_result)
    
    return None

def cache_calculation_result(station_id, period, result):
    """缓存计算结果"""
    cache_key = f"score:{station_id}:{period}"
    redis_client.setex(
        cache_key, 
        3600,  # 1小时过期
        json.dumps(result, default=str)
    )
```

## 九、质量控制

### 9.1 结果验证
```python
def validate_score_result(score_data):
    """验证评分结果"""
    validations = []
    
    # 分数范围检查
    if not (0 <= score_data['total_score'] <= 100):
        validations.append("总分超出有效范围")
    
    # 逻辑一致性检查
    calculated_total = (
        score_data['operation_score'] * 0.5 +
        score_data['service_score'] * 0.3 +
        score_data['stability_score'] * 0.2
    )
    
    if abs(calculated_total - score_data['total_score']) > 0.1:
        validations.append("总分计算不一致")
    
    return len(validations) == 0, validations
```

### 9.2 数据质量监控
```python
def monitor_data_quality(calculation_results):
    """监控数据质量"""
    quality_metrics = {
        'total_stations': len(calculation_results),
        'success_rate': sum(1 for r in calculation_results if r['success']) / len(calculation_results),
        'avg_score': np.mean([r['total_score'] for r in calculation_results if r['success']]),
        'score_distribution': {},
        'anomaly_count': 0
    }
    
    # 统计分数分布
    scores = [r['total_score'] for r in calculation_results if r['success']]
    quality_metrics['score_distribution'] = {
        'S': sum(1 for s in scores if s >= 90),
        'A': sum(1 for s in scores if 80 <= s < 90),
        'B': sum(1 for s in scores if 70 <= s < 80),
        'C': sum(1 for s in scores if 60 <= s < 70),
        'D': sum(1 for s in scores if s < 60)
    }
    
    return quality_metrics
```
