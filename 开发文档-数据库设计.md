# 充电场站评分系统 - 数据库设计文档

## 一、数据库设计原则

### 1.1 设计目标
- **性能优化**: 支持高并发查询和快速数据检索
- **数据完整性**: 确保评分数据的准确性和一致性
- **扩展性**: 支持未来功能扩展和数据量增长
- **维护性**: 便于数据维护和历史数据管理

### 1.2 设计原则
- 遵循第三范式，避免数据冗余
- 合理使用索引提升查询性能
- 分离热数据和冷数据
- 支持数据分区和归档

## 二、核心表结构设计

### 2.1 场站评分主表 (station_scores)

**表名**: `station_scores`
**用途**: 存储场站的综合评分和基础统计信息

```sql
CREATE TABLE station_scores (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    station_id VARCHAR(64) NOT NULL COMMENT '场站ID',
    station_name VARCHAR(200) NOT NULL COMMENT '场站名称',
    city_name VARCHAR(300) NOT NULL COMMENT '城市名称',
    
    -- 评分数据
    total_score DECIMAL(5,2) NOT NULL COMMENT '总评分 0-100',
    grade CHAR(1) NOT NULL COMMENT '评分等级 S/A/B/C/D',
    
    -- 分维度评分
    operation_score DECIMAL(5,2) NOT NULL COMMENT '运营效率评分',
    service_score DECIMAL(5,2) NOT NULL COMMENT '服务质量评分', 
    stability_score DECIMAL(5,2) NOT NULL COMMENT '稳定性评分',
    
    -- 排名信息
    city_rank INT COMMENT '城市排名',
    national_rank INT COMMENT '全国排名',
    
    -- 统计周期
    stat_period VARCHAR(20) NOT NULL COMMENT '统计周期 YYYY-MM',
    stat_start_date DATE NOT NULL COMMENT '统计开始日期',
    stat_end_date DATE NOT NULL COMMENT '统计结束日期',
    
    -- 基础统计数据
    total_orders INT NOT NULL DEFAULT 0 COMMENT '总订单数',
    completed_orders INT NOT NULL DEFAULT 0 COMMENT '完成订单数',
    total_revenue DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '总收益(元)',
    device_count INT NOT NULL DEFAULT 0 COMMENT '设备数量',
    operating_days INT NOT NULL DEFAULT 0 COMMENT '运营天数',
    unique_users INT NOT NULL DEFAULT 0 COMMENT '独立用户数',
    
    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 约束和索引
    UNIQUE KEY uk_station_period (station_id, stat_period),
    INDEX idx_total_score (total_score DESC),
    INDEX idx_city_score (city_name, total_score DESC),
    INDEX idx_grade (grade),
    INDEX idx_period (stat_period),
    INDEX idx_city_rank (city_name, city_rank),
    INDEX idx_national_rank (national_rank),
    INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场站评分主表';
```

### 2.2 评分详细指标表 (station_score_details)

**表名**: `station_score_details`
**用途**: 存储各项详细评分指标

```sql
CREATE TABLE station_score_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    station_id VARCHAR(64) NOT NULL COMMENT '场站ID',
    stat_period VARCHAR(20) NOT NULL COMMENT '统计周期',
    
    -- 运营效率指标 (50%权重)
    daily_revenue_rate DECIMAL(10,2) COMMENT '日均收益率(元/天) = total_fees / 运营天数',
    device_utilization DECIMAL(5,2) COMMENT '设备利用率(%) = 充电订单数 / (设备数量 × 运营天数)',
    revenue_per_device DECIMAL(10,2) COMMENT '单设备产出(元/设备) = total_fees / dc_ac_num',
    completion_rate DECIMAL(5,2) COMMENT '充电完成率(%) = 完成订单数 / 总订单数',

    -- 服务质量指标 (30%权重)
    avg_charge_time DECIMAL(6,2) COMMENT '平均充电时长(小时) = AVG(timelong)',
    avg_charge_power DECIMAL(8,4) COMMENT '平均充电量(kWh) = AVG(charge_power)',
    user_retention_rate DECIMAL(5,2) COMMENT '用户复购率(%) = 重复用户数 / 总用户数',
    failure_rate DECIMAL(5,2) COMMENT '故障率(%) = 异常结束订单数 / 总订单数',

    -- 稳定性指标 (20%权重)
    order_growth_trend DECIMAL(6,2) COMMENT '订单增长趋势(%) = (近期订单-历史订单)/历史订单*100',
    revenue_stability DECIMAL(5,2) COMMENT '收益稳定性(%) = 1 - (收益标准差/收益均值)',
    operation_continuity DECIMAL(5,2) COMMENT '运营连续性(%) = 连续运营天数 / 总天数',
    device_health_rate DECIMAL(5,2) COMMENT '设备健康度(%) = 正常运行设备数 / 总设备数',
    time_balance_index DECIMAL(5,2) COMMENT '时段均衡性指数 = 基于不同时段利用率方差计算',

    -- 基础统计数据 (用于计算上述指标)
    total_orders INT COMMENT '总订单数',
    completed_orders INT COMMENT '完成订单数',
    failed_orders INT COMMENT '异常订单数',
    total_revenue DECIMAL(12,2) COMMENT '总收益(元)',
    operating_days INT COMMENT '运营天数',
    device_count INT COMMENT '设备数量',
    unique_users INT COMMENT '独立用户数',
    repeat_users INT COMMENT '复购用户数',

    -- 时段分析数据
    peak_hour_orders INT COMMENT '高峰期订单数(8-10点,18-20点)',
    off_peak_orders INT COMMENT '非高峰期订单数',
    weekend_orders INT COMMENT '周末订单数',
    weekday_orders INT COMMENT '工作日订单数',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_station_period_detail (station_id, stat_period),
    INDEX idx_period_detail (stat_period),
    INDEX idx_daily_revenue (daily_revenue DESC),
    INDEX idx_device_utilization (device_utilization DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场站评分详细指标表';
```

### 2.3 评分计算日志表 (score_calculation_logs)

**表名**: `score_calculation_logs`
**用途**: 记录评分计算任务的执行情况

```sql
CREATE TABLE score_calculation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    batch_id VARCHAR(64) NOT NULL COMMENT '批次ID',
    stat_period VARCHAR(20) NOT NULL COMMENT '统计周期',
    
    -- 任务信息
    task_type ENUM('FULL', 'INCREMENTAL', 'SINGLE') DEFAULT 'FULL' COMMENT '任务类型',
    trigger_type ENUM('MANUAL', 'SCHEDULED', 'API') DEFAULT 'SCHEDULED' COMMENT '触发方式',
    trigger_user VARCHAR(64) COMMENT '触发用户',
    
    -- 执行统计
    total_stations INT NOT NULL DEFAULT 0 COMMENT '总场站数',
    success_count INT NOT NULL DEFAULT 0 COMMENT '成功计算数',
    failed_count INT NOT NULL DEFAULT 0 COMMENT '失败计算数',
    skipped_count INT NOT NULL DEFAULT 0 COMMENT '跳过计算数',
    
    -- 时间信息
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration_seconds INT COMMENT '耗时(秒)',
    
    -- 状态信息
    status ENUM('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '执行状态',
    progress_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '进度百分比',
    error_message TEXT COMMENT '错误信息',
    
    -- 结果统计
    result_summary JSON COMMENT '结果摘要(JSON格式)',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_batch_id (batch_id),
    INDEX idx_period_status (stat_period, status),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分计算日志表';
```

### 2.4 评分历史趋势表 (station_score_trends)

**表名**: `station_score_trends`
**用途**: 存储场站评分的历史变化趋势

```sql
CREATE TABLE station_score_trends (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    station_id VARCHAR(64) NOT NULL COMMENT '场站ID',
    stat_period VARCHAR(20) NOT NULL COMMENT '统计周期',
    
    -- 评分变化
    total_score DECIMAL(5,2) NOT NULL COMMENT '当期总评分',
    prev_total_score DECIMAL(5,2) COMMENT '上期总评分',
    score_change DECIMAL(6,2) COMMENT '评分变化',
    score_change_rate DECIMAL(5,2) COMMENT '评分变化率(%)',
    
    -- 排名变化
    city_rank INT COMMENT '当期城市排名',
    prev_city_rank INT COMMENT '上期城市排名',
    city_rank_change INT COMMENT '城市排名变化',
    
    national_rank INT COMMENT '当期全国排名',
    prev_national_rank INT COMMENT '上期全国排名',
    national_rank_change INT COMMENT '全国排名变化',
    
    -- 等级变化
    grade CHAR(1) NOT NULL COMMENT '当期等级',
    prev_grade CHAR(1) COMMENT '上期等级',
    grade_changed BOOLEAN DEFAULT FALSE COMMENT '等级是否变化',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_station_period_trend (station_id, stat_period),
    INDEX idx_period_trend (stat_period),
    INDEX idx_score_change (score_change DESC),
    INDEX idx_rank_change (city_rank_change, national_rank_change)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场站评分历史趋势表';
```

## 三、视图设计

### 3.1 场站评分汇总视图

```sql
CREATE VIEW v_station_score_summary AS
SELECT 
    ss.station_id,
    ss.station_name,
    ss.city_name,
    ss.total_score,
    ss.grade,
    ss.city_rank,
    ss.national_rank,
    ss.stat_period,
    
    -- 详细指标
    ssd.daily_revenue,
    ssd.device_utilization,
    ssd.completion_rate,
    ssd.avg_charge_time,
    ssd.user_retention_rate,
    ssd.revenue_stability,
    
    -- 趋势信息
    sst.score_change,
    sst.city_rank_change,
    sst.national_rank_change,
    
    ss.update_time
FROM station_scores ss
LEFT JOIN station_score_details ssd ON ss.station_id = ssd.station_id AND ss.stat_period = ssd.stat_period
LEFT JOIN station_score_trends sst ON ss.station_id = sst.station_id AND ss.stat_period = sst.stat_period
WHERE ss.stat_period = (
    SELECT MAX(stat_period) FROM station_scores WHERE station_id = ss.station_id
);
```

### 3.2 城市评分统计视图

```sql
CREATE VIEW v_city_score_statistics AS
SELECT 
    city_name,
    stat_period,
    COUNT(*) as station_count,
    AVG(total_score) as avg_score,
    MIN(total_score) as min_score,
    MAX(total_score) as max_score,
    STDDEV(total_score) as score_stddev,
    
    -- 等级分布
    SUM(CASE WHEN grade = 'S' THEN 1 ELSE 0 END) as grade_s_count,
    SUM(CASE WHEN grade = 'A' THEN 1 ELSE 0 END) as grade_a_count,
    SUM(CASE WHEN grade = 'B' THEN 1 ELSE 0 END) as grade_b_count,
    SUM(CASE WHEN grade = 'C' THEN 1 ELSE 0 END) as grade_c_count,
    SUM(CASE WHEN grade = 'D' THEN 1 ELSE 0 END) as grade_d_count,
    
    -- 业务统计
    SUM(total_orders) as total_orders,
    SUM(total_revenue) as total_revenue,
    SUM(device_count) as total_devices,
    AVG(device_utilization) as avg_device_utilization
    
FROM station_scores ss
LEFT JOIN station_score_details ssd ON ss.station_id = ssd.station_id AND ss.stat_period = ssd.stat_period
GROUP BY city_name, stat_period;
```

## 四、索引优化策略

### 4.1 主要查询场景
```sql
-- 1. 按城市查询排行榜
SELECT * FROM station_scores 
WHERE city_name = '北京市' AND stat_period = '2025-01'
ORDER BY total_score DESC LIMIT 10;

-- 2. 全国排行榜查询
SELECT * FROM station_scores 
WHERE stat_period = '2025-01'
ORDER BY total_score DESC LIMIT 50;

-- 3. 单个场站详情查询
SELECT * FROM station_scores ss
LEFT JOIN station_score_details ssd ON ss.station_id = ssd.station_id
WHERE ss.station_id = '123' AND ss.stat_period = '2025-01';

-- 4. 评分分布统计
SELECT grade, COUNT(*) FROM station_scores 
WHERE stat_period = '2025-01' 
GROUP BY grade;
```

### 4.2 复合索引设计
```sql
-- 城市排行榜查询优化
ALTER TABLE station_scores ADD INDEX idx_city_period_score (city_name, stat_period, total_score DESC);

-- 全国排行榜查询优化  
ALTER TABLE station_scores ADD INDEX idx_period_score (stat_period, total_score DESC);

-- 场站详情查询优化
ALTER TABLE station_score_details ADD INDEX idx_station_period (station_id, stat_period);

-- 趋势分析查询优化
ALTER TABLE station_score_trends ADD INDEX idx_station_periods (station_id, stat_period DESC);
```

## 五、数据分区策略

### 5.1 按时间分区
```sql
-- 对历史数据表进行分区
ALTER TABLE station_scores 
PARTITION BY RANGE (YEAR(STR_TO_DATE(stat_period, '%Y-%m'))) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 5.2 数据归档策略
```sql
-- 创建归档表
CREATE TABLE station_scores_archive LIKE station_scores;

-- 归档12个月前的数据
INSERT INTO station_scores_archive 
SELECT * FROM station_scores 
WHERE STR_TO_DATE(stat_period, '%Y-%m') < DATE_SUB(CURDATE(), INTERVAL 12 MONTH);
```

## 六、数据一致性保证

### 6.1 事务处理
```sql
-- 评分计算事务示例
START TRANSACTION;

-- 1. 插入或更新主评分表
INSERT INTO station_scores (...) VALUES (...) 
ON DUPLICATE KEY UPDATE ...;

-- 2. 插入详细指标
INSERT INTO station_score_details (...) VALUES (...) 
ON DUPLICATE KEY UPDATE ...;

-- 3. 更新趋势表
INSERT INTO station_score_trends (...) VALUES (...) 
ON DUPLICATE KEY UPDATE ...;

-- 4. 记录计算日志
UPDATE score_calculation_logs 
SET success_count = success_count + 1 
WHERE batch_id = ?;

COMMIT;
```

### 6.2 数据校验
```sql
-- 数据完整性检查
SELECT 
    COUNT(*) as total_scores,
    COUNT(CASE WHEN ssd.station_id IS NULL THEN 1 END) as missing_details,
    COUNT(CASE WHEN total_score < 0 OR total_score > 100 THEN 1 END) as invalid_scores
FROM station_scores ss
LEFT JOIN station_score_details ssd ON ss.station_id = ssd.station_id AND ss.stat_period = ssd.stat_period
WHERE ss.stat_period = '2025-01';
```

## 七、性能监控

### 7.1 慢查询监控
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询
SELECT * FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY query_time DESC;
```

### 7.2 索引使用分析
```sql
-- 分析索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'chargepile-v3.0' 
AND TABLE_NAME LIKE 'station_score%';
```

## 八、备份和恢复

### 8.1 备份策略
```bash
# 每日全量备份
mysqldump --single-transaction --routines --triggers \
  chargepile-v3.0 station_scores station_score_details \
  > backup_$(date +%Y%m%d).sql

# 增量备份(基于binlog)
mysqlbinlog --start-datetime="2025-01-15 00:00:00" \
  --stop-datetime="2025-01-15 23:59:59" \
  mysql-bin.000001 > incremental_backup.sql
```

### 8.2 数据恢复
```bash
# 恢复全量备份
mysql chargepile-v3.0 < backup_20250115.sql

# 恢复增量备份
mysql chargepile-v3.0 < incremental_backup.sql
```
