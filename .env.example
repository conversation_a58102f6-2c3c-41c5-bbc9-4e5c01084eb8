# 应用配置
APP_NAME=充电场站评分系统
APP_VERSION=1.0.0
DEBUG=False

# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/chargepile-v3.0
DATABASE_ECHO=False

# API配置
API_V1_PREFIX=/api/v1

# 评分计算配置
SCORE_CALCULATION_BATCH_SIZE=50
SCORE_CALCULATION_TIMEOUT=3600

# 数据质量要求
MIN_OPERATING_DAYS=30
MIN_ORDERS_COUNT=50
MIN_REVENUE_AMOUNT=1000.0

# 权重配置
OPERATION_EFFICIENCY_WEIGHT=0.5
SERVICE_QUALITY_WEIGHT=0.2
STABILITY_WEIGHT=0.3

# 运营效率指标权重
DAILY_REVENUE_RATE_WEIGHT=0.30
DEVICE_UTILIZATION_WEIGHT=0.25
REVENUE_PER_DEVICE_WEIGHT=0.25
COMPLETION_RATE_WEIGHT=0.20

# 服务质量指标权重
AVG_CHARGE_TIME_WEIGHT=0.25
AVG_CHARGE_POWER_WEIGHT=0.30
USER_RETENTION_RATE_WEIGHT=0.30
FAILURE_RATE_WEIGHT=0.15

# 稳定性指标权重
ORDER_GROWTH_TREND_WEIGHT=0.20
REVENUE_STABILITY_WEIGHT=0.25
OPERATION_CONTINUITY_WEIGHT=0.20
DEVICE_HEALTH_RATE_WEIGHT=0.20
TIME_BALANCE_INDEX_WEIGHT=0.15

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 定时任务配置
ENABLE_SCHEDULER=True
MONTHLY_CALCULATION_HOUR=2
